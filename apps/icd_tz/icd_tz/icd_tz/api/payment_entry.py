import frappe
from frappe.utils import flt


def on_submit(doc, method):
    """Handle Payment Entry submission to clear sales invoice references when invoices are fully paid"""
    clear_paid_invoice_references(doc)


def on_cancel(doc, method):
    """Handle Payment Entry cancellation to restore sales invoice references"""
    restore_cancelled_invoice_references(doc)


def clear_paid_invoice_references(payment_entry):
    """Clear sales invoice references from container charges when invoices are fully paid"""
    
    # Get all Sales Invoice references from the payment entry
    sales_invoices = []
    for reference in payment_entry.references:
        if reference.reference_doctype == "Sales Invoice":
            sales_invoices.append(reference.reference_name)
    
    if not sales_invoices:
        return
    
    # Check each sales invoice to see if it's fully paid
    for invoice_name in sales_invoices:
        invoice_doc = frappe.get_doc("Sales Invoice", invoice_name)
        
        # Check if invoice is fully paid (outstanding amount is 0)
        if flt(invoice_doc.outstanding_amount) == 0:
            clear_invoice_references_from_charges(invoice_name)


def restore_cancelled_invoice_references(payment_entry):
    """Restore sales invoice references when payment is cancelled"""
    
    # Get all Sales Invoice references from the payment entry
    sales_invoices = []
    for reference in payment_entry.references:
        if reference.reference_doctype == "Sales Invoice":
            sales_invoices.append(reference.reference_name)
    
    if not sales_invoices:
        return
    
    # Restore invoice references for each sales invoice
    for invoice_name in sales_invoices:
        restore_invoice_references_to_charges(invoice_name)


def clear_invoice_references_from_charges(invoice_name):
    """Clear the sales invoice reference from container charges"""

    # Get the sales invoice document
    invoice_doc = frappe.get_doc("Sales Invoice", invoice_name)

    if not invoice_doc.m_bl_no:
        return

    # Get service types from ICD TZ Settings
    settings = frappe.get_doc("ICD TZ Settings")

    # Organize services by type
    transport_services = [row.service_name for row in settings.service_types if row.service_type == "Transport"]
    shore_services = [row.service_name for row in settings.service_types if row.service_type == "Shore"]
    stripping_services = [row.service_name for row in settings.service_types if row.service_type == "Stripping"]
    verification_services = [row.service_name for row in settings.service_types if row.service_type == "Verification"]
    removal_services = [row.service_name for row in settings.service_types if row.service_type == "Removal"]
    corridor_services = [row.service_name for row in settings.service_types if row.service_type == "Levy"]
    storage_services = [row.service_name for row in settings.service_types if row.service_type in ["Storage-Single", "Storage-Double"]]
    
    # Process each item in the invoice
    for item in invoice_doc.items:
        if item.item_code in transport_services:
            clear_container_reception_ref(item.container_id, "t_sales_invoice")
        
        elif item.item_code in shore_services:
            clear_container_reception_ref(item.container_id, "s_sales_invoice")
        
        elif item.item_code in stripping_services:
            clear_booking_refs(item.container_id, "s_sales_invoice")
        
        elif item.item_code in verification_services:
            clear_booking_refs(item.container_id, "cv_sales_invoice")
        
        elif item.item_code in removal_services:
            clear_container_refs(item.container_id, "r_sales_invoice")
        
        elif item.item_code in corridor_services:
            clear_container_refs(item.container_id, "c_sales_invoice")
        
        elif item.item_code in storage_services:
            clear_storage_date_refs(item.container_id, item.container_child_refs)
        
        else:
            clear_container_insp_refs(item.container_id, item.item_code)


def restore_invoice_references_to_charges(invoice_name):
    """Restore the sales invoice reference to container charges"""

    # Get the sales invoice document
    invoice_doc = frappe.get_doc("Sales Invoice", invoice_name)

    if not invoice_doc.m_bl_no:
        return

    # Get service types from ICD TZ Settings
    settings = frappe.get_doc("ICD TZ Settings")

    # Organize services by type
    transport_services = [row.service_name for row in settings.service_types if row.service_type == "Transport"]
    shore_services = [row.service_name for row in settings.service_types if row.service_type == "Shore"]
    stripping_services = [row.service_name for row in settings.service_types if row.service_type == "Stripping"]
    verification_services = [row.service_name for row in settings.service_types if row.service_type == "Verification"]
    removal_services = [row.service_name for row in settings.service_types if row.service_type == "Removal"]
    corridor_services = [row.service_name for row in settings.service_types if row.service_type == "Levy"]
    storage_services = [row.service_name for row in settings.service_types if row.service_type in ["Storage-Single", "Storage-Double"]]
    
    # Process each item in the invoice
    for item in invoice_doc.items:
        if item.item_code in transport_services:
            restore_container_reception_ref(item.container_id, invoice_name, "t_sales_invoice")
        
        elif item.item_code in shore_services:
            restore_container_reception_ref(item.container_id, invoice_name, "s_sales_invoice")
        
        elif item.item_code in stripping_services:
            restore_booking_refs(item.container_id, invoice_name, "s_sales_invoice")
        
        elif item.item_code in verification_services:
            restore_booking_refs(item.container_id, invoice_name, "cv_sales_invoice")
        
        elif item.item_code in removal_services:
            restore_container_refs(item.container_id, invoice_name, "r_sales_invoice")
        
        elif item.item_code in corridor_services:
            restore_container_refs(item.container_id, invoice_name, "c_sales_invoice")
        
        elif item.item_code in storage_services:
            restore_storage_date_refs(item.container_id, invoice_name, item.container_child_refs)
        
        else:
            restore_container_insp_refs(item.container_id, item.item_code, invoice_name)


def clear_container_reception_ref(container_id, field):
    """Clear sales invoice reference from Container Reception"""
    container_reception = frappe.db.get_value("Container", container_id, "container_reception")
    
    if container_reception:
        frappe.db.set_value("Container Reception", container_reception, field, None)


def clear_booking_refs(container_id, field):
    """Clear sales invoice reference from In Yard Container Booking"""
    filters = {"container_id": container_id, "docstatus": 1}
    booking_ids = frappe.db.get_all("In Yard Container Booking", filters, pluck="name")
    
    for booking_id in booking_ids:
        frappe.db.set_value("In Yard Container Booking", booking_id, field, None)


def clear_container_refs(container_id, field):
    """Clear sales invoice reference from Container"""
    frappe.db.set_value("Container", container_id, field, None)


def clear_storage_date_refs(container_id, child_refs):
    """Clear sales invoice reference from Container storage dates"""
    container_doc = frappe.get_doc("Container", container_id)
    for child in container_doc.container_dates:
        if child.name in child_refs:
            child.sales_invoice = None
    container_doc.save(ignore_permissions=True)


def clear_container_insp_refs(container_id, item_code):
    """Clear sales invoice reference from Container Inspection"""
    container_inspections = frappe.db.get_all("Container Inspection", {"container_id": container_id, "docstatus": 1}, pluck="name")
    
    for inspection in container_inspections:
        insp_doc = frappe.get_doc("Container Inspection", inspection)
        for row in insp_doc.services:
            if row.service == item_code:
                frappe.db.set_value("Container Inspection Detail", row.name, "sales_invoice", None)


def restore_container_reception_ref(container_id, invoice_name, field):
    """Restore sales invoice reference to Container Reception"""
    container_reception = frappe.db.get_value("Container", container_id, "container_reception")
    
    if container_reception:
        frappe.db.set_value("Container Reception", container_reception, field, invoice_name)


def restore_booking_refs(container_id, invoice_name, field):
    """Restore sales invoice reference to In Yard Container Booking"""
    filters = {"container_id": container_id, "docstatus": 1}
    booking_ids = frappe.db.get_all("In Yard Container Booking", filters, pluck="name")
    
    for booking_id in booking_ids:
        frappe.db.set_value("In Yard Container Booking", booking_id, field, invoice_name)


def restore_container_refs(container_id, invoice_name, field):
    """Restore sales invoice reference to Container"""
    frappe.db.set_value("Container", container_id, field, invoice_name)


def restore_storage_date_refs(container_id, invoice_name, child_refs):
    """Restore sales invoice reference to Container storage dates"""
    container_doc = frappe.get_doc("Container", container_id)
    for child in container_doc.container_dates:
        if child.name in child_refs:
            child.sales_invoice = invoice_name
    container_doc.save(ignore_permissions=True)


def restore_container_insp_refs(container_id, item_code, invoice_name):
    """Restore sales invoice reference to Container Inspection"""
    container_inspections = frappe.db.get_all("Container Inspection", {"container_id": container_id, "docstatus": 1}, pluck="name")
    
    for inspection in container_inspections:
        insp_doc = frappe.get_doc("Container Inspection", inspection)
        for row in insp_doc.services:
            if row.service == item_code:
                frappe.db.set_value("Container Inspection Detail", row.name, "sales_invoice", invoice_name)
